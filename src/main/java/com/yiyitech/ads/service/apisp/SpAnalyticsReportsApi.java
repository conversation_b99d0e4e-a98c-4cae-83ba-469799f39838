package com.yiyitech.ads.service.apisp;

import com.alibaba.druid.util.StringUtils;
import com.yiyitech.ads.model.response.SpAnalyticsReportResponse;
import com.yiyitech.ads.exception.BasicExceptionCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import com.yiyitech.support.exception.BusinessException;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Amazon SP-API Analytics Reports Service
 * Used to retrieve various analytics report data from Amazon SP-API
 * Including search terms reports, search catalog performance reports, market basket analysis, etc.
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName SpAnalyticsReportsApi.java
 * @Description Amazon SP-API analytics reports service for search terms and category data
 * @createTime 2025-01-31
 */
@Slf4j
@Component
public class SpAnalyticsReportsApi {

        private static final int MAX_RETRIES = 3;
        private static final long RETRY_DELAY_MS = 2000;
        private static final List<String> VALID_REGIONS = Arrays.asList("na", "eu", "fe");

        private final RestTemplate awsSignedRestTemplate;

        @Value("${amazon.spapi.api_url:}") // No default value
        private String apiUrl;

        @Value("${amazon.spapi.region:na}")
        private String region;

        public SpAnalyticsReportsApi(@Qualifier("awsSignedRestTemplate") RestTemplate awsSignedRestTemplate) {
            this.awsSignedRestTemplate = awsSignedRestTemplate;
        }

        @PostConstruct
        public void validateConfig() {
            // If apiUrl is not configured, use dynamic endpoint
            if (StringUtils.isEmpty(apiUrl)) {
                apiUrl = getApiUrl();
                log.info("apiUrl not configured, using dynamic endpoint: {}", apiUrl);
            }

            if (apiUrl.contains("advertising-api")) {
                log.error("!!!!!!!! CRITICAL ERROR: Advertising API endpoint configured !!!!!!!!");
                log.error("Current value: {}", apiUrl);
                log.error("Please fix configuration immediately: amazon.spapi.api_url");
            }

            if (!VALID_REGIONS.contains(region)) {
                log.error("Invalid region configuration: {}", region);
                log.error("Valid values: na/eu/fe");
            }
        }

        // Dynamic endpoint construction (preferred)
        private String getApiUrl() {
            return "https://sellingpartnerapi-" + region + ".amazon.com";
        }

        public SpAnalyticsReportResponse createSearchQueryPerformanceReport(String accessToken,
                                                                            List<String> marketplaceIds,
                                                                            String startDate,
                                                                            String endDate) {
            int retryCount = 0;

            while (retryCount <= MAX_RETRIES) {
                try {
                    log.info("Creating search query performance report - start date: {}, end date: {}, marketplaces: {}", startDate, endDate, marketplaceIds);

                    // Use dynamic endpoint construction (preferred)
                    String baseUrl = getApiUrl();

                    // Log which endpoint is being used
                    log.info("API endpoint: {}", baseUrl);

                    // Build complete URL
                    String url = baseUrl + "/reports/2021-06-30/reports";

                    Map<String, Object> requestBody = new HashMap<>();
                    requestBody.put("reportType", "GET_AMAZON_SEARCH_TERMS_REPORT"); // Ensure correct report type
                    requestBody.put("marketplaceIds", marketplaceIds);
                    requestBody.put("dataStartTime", startDate + "T00:00:00Z");
                    requestBody.put("dataEndTime", endDate + "T23:59:59Z");

                    HttpHeaders headers = new HttpHeaders();
                    headers.set("x-amz-access-token", accessToken); // Required authentication header
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

                    // ==== Send request ====
                    log.debug("Sending request to: {}", url);
                    ResponseEntity<SpAnalyticsReportResponse> response = awsSignedRestTemplate.exchange(
                            url, HttpMethod.POST, entity, SpAnalyticsReportResponse.class
                    );

                    // ==== Handle response ====
                    if (response.getStatusCode() != HttpStatus.ACCEPTED) {
                        log.error("Report creation failed - status code: {}", response.getStatusCode());
                        throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_CODE3);
                    }

                    if (response.getBody() == null) {
                        log.error("Report creation succeeded but response body is empty");
                        throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_CODE2);
                    }

                    log.info("Report created successfully - ID: {}", response.getBody().getReportId());
                    return response.getBody();

                } catch (ResourceAccessException ex) {
                    // Network issue retry
                    retryCount++;
                    if (retryCount > MAX_RETRIES) {
                        log.error("Connection timeout, maximum retry attempts reached");
                        throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_CODE4);
                    }
                    log.warn("Connection timeout, retry attempt {} ...", retryCount);
                    try {
                        Thread.sleep(RETRY_DELAY_MS * retryCount);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new BusinessException(BasicExceptionCode.BASIC_CODE, "Thread interrupted");
                    }
                } catch (Exception e) {
                    log.error("Report creation exception", e);
                    throw new BusinessException(BasicExceptionCode.BASIC_CODE1, e.getMessage());
                }
            }

            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }

    /**
     * Create search catalog performance report
     * Corresponds to Amazon SP-API's Search Catalog Performance Report
     * Used to retrieve search term and product category association performance data
     *
     * @param accessToken SP-API access token
     * @param marketplaceIds List of marketplace IDs
     * @param startDate Start date
     * @param endDate End date
     * @return Report creation response
     */
    public SpAnalyticsReportResponse createSearchCatalogPerformanceReport(String accessToken,
                                                                          List<String> marketplaceIds,
                                                                          String startDate,
                                                                          String endDate) {
        try {
            log.info("Creating search catalog performance report - start date: {}, end date: {}, marketplaces: {}", startDate, endDate, marketplaceIds);
            
            String url = apiUrl + "/reports/2021-06-30/reports";
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("reportType", "GET_BRAND_ANALYTICS_SEARCH_CATALOG_PERFORMANCE_REPORT");
            requestBody.put("marketplaceIds", marketplaceIds);
            requestBody.put("dataStartTime", startDate + "T00:00:00Z");
            requestBody.put("dataEndTime", endDate + "T23:59:59Z");
            
            Map<String, Object> reportOptions = new HashMap<>();
            reportOptions.put("reportPeriod", "DAY");
            requestBody.put("reportOptions", reportOptions);

            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<SpAnalyticsReportResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.POST, entity, SpAnalyticsReportResponse.class
            );

            if (response.getStatusCode() != HttpStatus.ACCEPTED || response.getBody() == null) {
                log.error("Search catalog performance report creation failed - status code: {}", response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "Search catalog performance report creation failed");
            }

            log.info("Search catalog performance report created successfully - report ID: {}", response.getBody().getReportId());
            return response.getBody();

        } catch (Exception e) {
            log.error("Search catalog performance report creation exception: {}", e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "Search catalog performance report creation exception: " + e.getMessage());
        }
    }

    /**
     * Create Amazon search terms report
     * Corresponds to Amazon SP-API's Amazon Search Terms Report
     * Used to retrieve Amazon platform search term data and trends
     *
     * @param accessToken SP-API access token
     * @param marketplaceIds List of marketplace IDs
     * @param startDate Start date
     * @param endDate End date
     * @return Report creation response
     */
    public SpAnalyticsReportResponse createAmazonSearchTermsReport(String accessToken,
                                                                  List<String> marketplaceIds,
                                                                  String startDate,
                                                                  String endDate) {
        try {
            log.info("Creating Amazon search terms report - start date: {}, end date: {}, marketplaces: {}", startDate, endDate, marketplaceIds);
            
            String url = apiUrl + "/reports/2021-06-30/reports";
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("reportType", "GET_BRAND_ANALYTICS_AMAZON_SEARCH_TERMS_REPORT");
            requestBody.put("marketplaceIds", marketplaceIds);
            requestBody.put("dataStartTime", startDate + "T00:00:00Z");
            requestBody.put("dataEndTime", endDate + "T23:59:59Z");
            
            Map<String, Object> reportOptions = new HashMap<>();
            reportOptions.put("reportPeriod", "WEEK"); // Amazon search terms reports are typically aggregated weekly
            requestBody.put("reportOptions", reportOptions);

            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<SpAnalyticsReportResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.POST, entity, SpAnalyticsReportResponse.class
            );

            if (response.getStatusCode() != HttpStatus.ACCEPTED || response.getBody() == null) {
                log.error("Amazon search terms report creation failed - status code: {}", response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "Amazon search terms report creation failed");
            }

            log.info("Amazon search terms report created successfully - report ID: {}", response.getBody().getReportId());
            return response.getBody();

        } catch (Exception e) {
            log.error("Amazon search terms report creation exception: {}", e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "Amazon search terms report creation exception: " + e.getMessage());
        }
    }

    /**
     * Get report status and download link
     *
     * @param accessToken SP-API access token
     * @param reportId Report ID
     * @return Report status response
     */
    public SpAnalyticsReportResponse getReportStatus(String accessToken, String reportId) {
        try {
            log.info("Getting report status - report ID: {}", reportId);
            
            String url = apiUrl + "/reports/2021-06-30/reports/" + reportId;
            
            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<SpAnalyticsReportResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.GET, entity, SpAnalyticsReportResponse.class
            );

            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("Get report status failed - report ID: {}, status code: {}", reportId, response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "Get report status failed");
            }

            log.info("Report status retrieved successfully - report ID: {}, status: {}", reportId, response.getBody().getProcessingStatus());
            return response.getBody();

        } catch (Exception e) {
            log.error("Get report status exception - report ID: {}, error: {}", reportId, e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "Get report status exception: " + e.getMessage());
        }
    }

    /**
     * Download report data
     *
     * @param accessToken SP-API access token
     * @param documentId Document ID
     * @return Report data (JSON string)
     */
    public String downloadReportData(String accessToken, String documentId) {
        try {
            log.info("Downloading report data - document ID: {}", documentId);
            
            String url = apiUrl + "/reports/2021-06-30/documents/" + documentId;
            
            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.GET, entity, String.class
            );

            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("Download report data failed - document ID: {}, status code: {}", documentId, response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "Download report data failed");
            }

            log.info("Report data downloaded successfully - document ID: {}, data size: {} bytes", documentId, response.getBody().length());
            return response.getBody();

        } catch (Exception e) {
            log.error("Download report data exception - document ID: {}, error: {}", documentId, e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "Download report data exception: " + e.getMessage());
        }
    }

    /**
     * Create HTTP request headers
     *
     * @param accessToken Access token
     * @return HTTP request headers
     */
    private HttpHeaders createHeaders(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.set("x-amz-access-token", accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("User-Agent", "YiyiTech-Ads-Analytics/1.0");
        return headers;
    }

    /**
     * Get yesterday's date string
     *
     * @return Yesterday's date (YYYY-MM-DD format)
     */
    public String getYesterdayDate() {
        return LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    /**
     * Get date string from one week ago
     *
     * @return Date from one week ago (YYYY-MM-DD format)
     */
    public String getWeekAgoDate() {
        return LocalDate.now().minusDays(7).format(DateTimeFormatter.ISO_LOCAL_DATE);
    }
}
