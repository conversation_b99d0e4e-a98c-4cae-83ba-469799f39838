package com.yiyitech.ads.service.apisp;

import com.alibaba.druid.util.StringUtils;
import com.yiyitech.ads.model.response.SpAnalyticsReportResponse;
import com.yiyitech.ads.exception.BasicExceptionCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import com.yiyitech.support.exception.BusinessException;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Amazon SP-API 分析报告服务
 * 用于获取Amazon SP-API的各种分析报告数据
 * 包括搜索词报告、搜索目录性能报告、市场篮分析等
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName SpAnalyticsReportsApi.java
 * @Description Amazon SP-API分析报告服务，获取搜索词和类目相关数据
 * @createTime 2025年01月31日
 */
@Slf4j
@Component
public class SpAnalyticsReportsApi {

        private static final int MAX_RETRIES = 3;
        private static final long RETRY_DELAY_MS = 2000;
        private static final List<String> VALID_REGIONS = Arrays.asList("na", "eu", "fe");

        private final RestTemplate awsSignedRestTemplate;

        @Value("${amazon.spapi.api_url:}") // 不再设默认值
        private String apiUrl;

        @Value("${amazon.spapi.region:na}")
        private String region;

        public SpAnalyticsReportsApi(@Qualifier("awsSignedRestTemplate") RestTemplate awsSignedRestTemplate) {
            this.awsSignedRestTemplate = awsSignedRestTemplate;
        }

        @PostConstruct
        public void validateConfig() {
            // 如果未配置apiUrl，则使用动态端点
            if (StringUtils.isEmpty(apiUrl)) {
                apiUrl = getApiUrl();
                log.info("未配置apiUrl，使用动态端点: {}", apiUrl);
            }

            if (apiUrl.contains("advertising-api")) {
                log.error("!!!!!!!! 严重错误: 配置了广告API端点 !!!!!!!!");
                log.error("当前值: {}", apiUrl);
                log.error("请立即修正配置: amazon.spapi.api_url");
            }

            if (!VALID_REGIONS.contains(region)) {
                log.error("无效区域配置: {}", region);
                log.error("有效值: na/eu/fe");
            }
        }

        // 动态构建端点（优先使用）
        private String getApiUrl() {
            return "https://sellingpartnerapi-" + region + ".amazon.com";
        }

        public SpAnalyticsReportResponse createSearchQueryPerformanceReport(String accessToken,
                                                                            List<String> marketplaceIds,
                                                                            String startDate,
                                                                            String endDate) {
            int retryCount = 0;

            while (retryCount <= MAX_RETRIES) {
                try {
                    log.info("创建搜索查询性能报告 - 开始日期: {}, 结束日期: {}, 市场: {}", startDate, endDate, marketplaceIds);

                    // 优先使用动态构建的端点
                    String baseUrl = getApiUrl();

                    // 打印使用哪个端点
                    log.info("API端点: {}", baseUrl);

                    // 构建完整URL
                    String url = baseUrl + "/reports/2021-06-30/reports";

                    Map<String, Object> requestBody = new HashMap<>();
                    requestBody.put("reportType", "GET_AMAZON_SEARCH_TERMS_REPORT"); // 确保报告类型正确
                    requestBody.put("marketplaceIds", marketplaceIds);
                    requestBody.put("dataStartTime", startDate + "T00:00:00Z");
                    requestBody.put("dataEndTime", endDate + "T23:59:59Z");

                    HttpHeaders headers = new HttpHeaders();
                    headers.set("x-amz-access-token", accessToken); // 必须的认证头
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

                    // ==== 发送请求 ====
                    log.debug("发送请求到: {}", url);
                    ResponseEntity<SpAnalyticsReportResponse> response = awsSignedRestTemplate.exchange(
                            url, HttpMethod.POST, entity, SpAnalyticsReportResponse.class
                    );

                    // ==== 处理响应 ====
                    if (response.getStatusCode() != HttpStatus.ACCEPTED) {
                        log.error("创建报告失败 - 状态码: {}", response.getStatusCode());
                        throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_CODE3);
                    }

                    if (response.getBody() == null) {
                        log.error("创建报告成功但响应体为空");
                        throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_CODE2);
                    }

                    log.info("报告创建成功 - ID: {}", response.getBody().getReportId());
                    return response.getBody();

                } catch (ResourceAccessException ex) {
                    // 网络问题重试
                    retryCount++;
                    if (retryCount > MAX_RETRIES) {
                        log.error("连接超时，已达最大重试次数");
                        throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_CODE4);
                    }
                    log.warn("连接超时，第 {} 次重试...", retryCount);
                    try {
                        Thread.sleep(RETRY_DELAY_MS * retryCount);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new BusinessException(BasicExceptionCode.BASIC_CODE, "线程被中断");
                    }
                } catch (Exception e) {
                    log.error("创建报告异常", e);
                    throw new BusinessException(BasicExceptionCode.BASIC_CODE1, e.getMessage());
                }
            }

            throw new BusinessException(BasicExceptionCode.BASIC_CODE, BasicExceptionCode.BASIC_MSG);
        }

    /**
     * 创建搜索目录性能报告
     * 对应Amazon SP-API的Search Catalog Performance Report
     * 用于获取搜索词与商品类目的关联性能数据
     *
     * @param accessToken SP-API访问令牌
     * @param marketplaceIds 市场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 报告创建响应
     */
    public SpAnalyticsReportResponse createSearchCatalogPerformanceReport(String accessToken,
                                                                          List<String> marketplaceIds,
                                                                          String startDate,
                                                                          String endDate) {
        try {
            log.info("创建搜索目录性能报告 - 开始日期: {}, 结束日期: {}, 市场: {}", startDate, endDate, marketplaceIds);
            
            String url = apiUrl + "/reports/2021-06-30/reports";
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("reportType", "GET_BRAND_ANALYTICS_SEARCH_CATALOG_PERFORMANCE_REPORT");
            requestBody.put("marketplaceIds", marketplaceIds);
            requestBody.put("dataStartTime", startDate + "T00:00:00Z");
            requestBody.put("dataEndTime", endDate + "T23:59:59Z");
            
            Map<String, Object> reportOptions = new HashMap<>();
            reportOptions.put("reportPeriod", "DAY");
            requestBody.put("reportOptions", reportOptions);

            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<SpAnalyticsReportResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.POST, entity, SpAnalyticsReportResponse.class
            );

            if (response.getStatusCode() != HttpStatus.ACCEPTED || response.getBody() == null) {
                log.error("创建搜索目录性能报告失败 - 状态码: {}", response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建搜索目录性能报告失败");
            }

            log.info("搜索目录性能报告创建成功 - 报告ID: {}", response.getBody().getReportId());
            return response.getBody();

        } catch (Exception e) {
            log.error("创建搜索目录性能报告异常: {}", e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建搜索目录性能报告异常: " + e.getMessage());
        }
    }

    /**
     * 创建Amazon搜索词报告
     * 对应Amazon SP-API的Amazon Search Terms Report
     * 用于获取Amazon平台的搜索词数据和趋势
     *
     * @param accessToken SP-API访问令牌
     * @param marketplaceIds 市场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 报告创建响应
     */
    public SpAnalyticsReportResponse createAmazonSearchTermsReport(String accessToken,
                                                                  List<String> marketplaceIds,
                                                                  String startDate,
                                                                  String endDate) {
        try {
            log.info("创建Amazon搜索词报告 - 开始日期: {}, 结束日期: {}, 市场: {}", startDate, endDate, marketplaceIds);
            
            String url = apiUrl + "/reports/2021-06-30/reports";
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("reportType", "GET_BRAND_ANALYTICS_AMAZON_SEARCH_TERMS_REPORT");
            requestBody.put("marketplaceIds", marketplaceIds);
            requestBody.put("dataStartTime", startDate + "T00:00:00Z");
            requestBody.put("dataEndTime", endDate + "T23:59:59Z");
            
            Map<String, Object> reportOptions = new HashMap<>();
            reportOptions.put("reportPeriod", "WEEK"); // Amazon搜索词报告通常按周统计
            requestBody.put("reportOptions", reportOptions);

            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<SpAnalyticsReportResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.POST, entity, SpAnalyticsReportResponse.class
            );

            if (response.getStatusCode() != HttpStatus.ACCEPTED || response.getBody() == null) {
                log.error("创建Amazon搜索词报告失败 - 状态码: {}", response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建Amazon搜索词报告失败");
            }

            log.info("Amazon搜索词报告创建成功 - 报告ID: {}", response.getBody().getReportId());
            return response.getBody();

        } catch (Exception e) {
            log.error("创建Amazon搜索词报告异常: {}", e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建Amazon搜索词报告异常: " + e.getMessage());
        }
    }

    /**
     * 获取报告状态和下载链接
     *
     * @param accessToken SP-API访问令牌
     * @param reportId 报告ID
     * @return 报告状态响应
     */
    public SpAnalyticsReportResponse getReportStatus(String accessToken, String reportId) {
        try {
            log.info("获取报告状态 - 报告ID: {}", reportId);
            
            String url = apiUrl + "/reports/2021-06-30/reports/" + reportId;
            
            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<SpAnalyticsReportResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.GET, entity, SpAnalyticsReportResponse.class
            );

            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("获取报告状态失败 - 报告ID: {}, 状态码: {}", reportId, response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "获取报告状态失败");
            }

            log.info("报告状态获取成功 - 报告ID: {}, 状态: {}", reportId, response.getBody().getProcessingStatus());
            return response.getBody();

        } catch (Exception e) {
            log.error("获取报告状态异常 - 报告ID: {}, 错误: {}", reportId, e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "获取报告状态异常: " + e.getMessage());
        }
    }

    /**
     * 下载报告数据
     *
     * @param accessToken SP-API访问令牌
     * @param documentId 文档ID
     * @return 报告数据（JSON字符串）
     */
    public String downloadReportData(String accessToken, String documentId) {
        try {
            log.info("下载报告数据 - 文档ID: {}", documentId);
            
            String url = apiUrl + "/reports/2021-06-30/documents/" + documentId;
            
            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.GET, entity, String.class
            );

            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("下载报告数据失败 - 文档ID: {}, 状态码: {}", documentId, response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "下载报告数据失败");
            }

            log.info("报告数据下载成功 - 文档ID: {}, 数据大小: {} bytes", documentId, response.getBody().length());
            return response.getBody();

        } catch (Exception e) {
            log.error("下载报告数据异常 - 文档ID: {}, 错误: {}", documentId, e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "下载报告数据异常: " + e.getMessage());
        }
    }

    /**
     * 创建HTTP请求头
     *
     * @param accessToken 访问令牌
     * @return HTTP请求头
     */
    private HttpHeaders createHeaders(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.set("x-amz-access-token", accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("User-Agent", "YiyiTech-Ads-Analytics/1.0");
        return headers;
    }

    /**
     * 获取昨天的日期字符串
     *
     * @return 昨天的日期 (YYYY-MM-DD格式)
     */
    public String getYesterdayDate() {
        return LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    /**
     * 获取一周前的日期字符串
     *
     * @return 一周前的日期 (YYYY-MM-DD格式)
     */
    public String getWeekAgoDate() {
        return LocalDate.now().minusDays(7).format(DateTimeFormatter.ISO_LOCAL_DATE);
    }
}
